<VirtualHost *:80>
ServerName www.oatechnologies.us

ServerAdmin webmaster@localhost
DocumentRoot /var/www/oatechnologies
    <Directory "/var/www/oatechnologies"> 
        Options Indexes FollowSymLinks
        AllowOverride All 
        Order allow,deny 
        allow from all 

        RewriteEngine On
        RewriteBase /
        RewriteRule ^index\.php$ - [L]
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule . /index.php [L]
    </Directory> 

ErrorLog ${APACHE_LOG_DIR}/oatechnologies_error.log
CustomLog ${APACHE_LOG_DIR}/oatechnologies_access.log combined

# Force HTTPS redirect to www subdomain
RewriteEngine on
RewriteCond %{SERVER_NAME} =www.oatechnologies.us
RewriteRule ^ https://www.oatechnologies.us%{REQUEST_URI} [END,NE,R=permanent]
RewriteCond %{SERVER_NAME} =www.oatechnologies.us
RewriteRule ^ https://%{SERVER_NAME}%{REQUEST_URI} [END,NE,R=permanent]
</VirtualHost>

# Redirect non-www to www (if oatechnologies.us ever points here)
<VirtualHost *:80>
ServerName oatechnologies.us
RewriteEngine on
RewriteRule ^ https://www.oatechnologies.us%{REQUEST_URI} [END,NE,R=permanent]
</VirtualHost>
