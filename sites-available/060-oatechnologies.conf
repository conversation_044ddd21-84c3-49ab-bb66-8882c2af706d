<VirtualHost *:80>
ServerName oatechnologies.us
ServerAlias www.oatechnologies.us

ServerAdmin webmaster@localhost
DocumentRoot /var/www/oatechnologies
    <Directory "/var/www/oatechnologies"> 
        Options Indexes FollowSymLinks
        AllowOverride All 
        Order allow,deny 
        allow from all 

        RewriteEngine On
        RewriteBase /
        RewriteRule ^index\.php$ - [L]
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule . /index.php [L]
    </Directory> 

ErrorLog ${APACHE_LOG_DIR}/oatechnologies_error.log
CustomLog ${APACHE_LOG_DIR}/oatechnologies_access.log combined

# Force HTTPS redirect
RewriteEngine on
RewriteCond %{SERVER_NAME} =oatechnologies.us [OR]
RewriteCond %{SERVER_NAME} =www.oatechnologies.us
RewriteRule ^ https://%{SERVER_NAME}%{REQUEST_URI} [END,NE,R=permanent]
</VirtualHost>
