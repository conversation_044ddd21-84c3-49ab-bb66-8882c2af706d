<VirtualHost *:80>
ServerName git.oatechnologies.us

ServerAdmin webmaster@localhost
DocumentRoot /var/www/git_oatechnologies
    <Directory "/var/www/git_oatechnologies"> 
        Options Indexes FollowSymLinks
        AllowOverride All 
        Order allow,deny 
        allow from all 

        RewriteEngine On
        RewriteBase /
        RewriteRule ^index\.php$ - [L]
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule . /index.php [L]
    </Directory> 

ErrorLog ${APACHE_LOG_DIR}/git_oatechnologies_error.log
CustomLog ${APACHE_LOG_DIR}/git_oatechnologies_access.log combined

# Force HTTPS redirect
RewriteEngine on
RewriteCond %{SERVER_NAME} =git.oatechnologies.us
RewriteRule ^ https://git.oatechnologies.us%{REQUEST_URI} [END,NE,R=permanent]
</VirtualHost>
