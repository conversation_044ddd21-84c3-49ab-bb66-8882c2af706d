<IfModule mod_ssl.c>
<VirtualHost *:443>
ServerName www.oatechnologies.us

ServerAdmin webmaster@localhost
DocumentRoot /var/www/oatechnologies
        <Directory "/var/www/oatechnologies"> 
         Options FollowSymLinks
         AllowOverride All  
         Order allow,deny
         Allow from all
       </Directory> 

ErrorLog ${APACHE_LOG_DIR}/oatechnologies_error.log
CustomLog ${APACHE_LOG_DIR}/oatechnologies_access.log combined

Include /etc/letsencrypt/options-ssl-apache.conf
SSLCertificateFile /etc/letsencrypt/live/www.oatechnologies.us/fullchain.pem
SSLCertificateKeyFile /etc/letsencrypt/live/www.oatechnologies.us/privkey.pem
</VirtualHost>

# Redirect non-www HTTPS to www HTTPS (if oatechnologies.us ever points here)
<VirtualHost *:443>
ServerName oatechnologies.us
RewriteEngine on
RewriteRule ^ https://www.oatechnologies.us%{REQUEST_URI} [END,NE,R=permanent]

Include /etc/letsencrypt/options-ssl-apache.conf
SSLCertificateFile /etc/letsencrypt/live/www.oatechnologies.us/fullchain.pem
SSLCertificateKeyFile /etc/letsencrypt/live/www.oatechnologies.us/privkey.pem
</VirtualHost>
</IfModule>
<IfModule mod_ssl.c>
<VirtualHost *:80>
ServerName www.oatechnologies.us

ServerAdmin webmaster@localhost
DocumentRoot /var/www/oatechnologies
    <Directory "/var/www/oatechnologies"> 
        Options Indexes FollowSymLinks
        AllowOverride All 
        Order allow,deny 
        allow from all 

        RewriteEngine On
        RewriteBase /
        RewriteRule ^index\.php$ - [L]
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule . /index.php [L]
    </Directory> 

ErrorLog ${APACHE_LOG_DIR}/oatechnologies_error.log
CustomLog ${APACHE_LOG_DIR}/oatechnologies_access.log combined

# Force HTTPS redirect to www subdomain
RewriteEngine on
# Some rewrite rules in this file were disabled on your HTTPS site,
# because they have the potential to create redirection loops.

# RewriteCond %{SERVER_NAME} =www.oatechnologies.us
# RewriteRule ^ https://www.oatechnologies.us%{REQUEST_URI} [END,NE,R=permanent]


</VirtualHost>
</IfModule>
