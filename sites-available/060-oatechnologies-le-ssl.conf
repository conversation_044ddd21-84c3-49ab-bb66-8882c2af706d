<IfModule mod_ssl.c>
<VirtualHost *:443>
ServerName oatechnologies.us
ServerAlias www.oatechnologies.us

ServerAdmin webmaster@localhost
DocumentRoot /var/www/oatechnologies
        <Directory "/var/www/oatechnologies"> 
         Options FollowSymLinks
         AllowOverride All  
         Order allow,deny
         Allow from all
       </Directory> 

ErrorLog ${APACHE_LOG_DIR}/oatechnologies_error.log
CustomLog ${APACHE_LOG_DIR}/oatechnologies_access.log combined

Include /etc/letsencrypt/options-ssl-apache.conf
SSLCertificateFile /etc/letsencrypt/live/oatechnologies.us/fullchain.pem
SSLCertificateKeyFile /etc/letsencrypt/live/oatechnologies.us/privkey.pem
</VirtualHost>
</IfModule>
