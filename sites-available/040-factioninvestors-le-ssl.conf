<IfModule mod_ssl.c>
<VirtualHost *:443>
    ServerName investors.factionnetworks.com
    ServerAdmin webmaster@localhost
    DocumentRoot /var/www/investors

    <Directory "/var/www/investors">
        Options -Indexes +FollowSymLinks
        AllowOverride None
        Order allow,deny
        allow from all

        RewriteEngine On
        RewriteBase /
        RewriteRule ^index\.php$ - [L]
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule . /index.php [L]
    </Directory>

    # Directory specific for protecting uploads/investors
    <Directory "/var/www/investors/wp-content/uploads/investors">
        Options -Indexes +FollowSymLinks
        AllowOverride None
        Order allow,deny
        allow from all

        RewriteEngine On
        RewriteBase /wp-content/uploads/investors/
        RewriteCond %{REQUEST_FILENAME} -f
        RewriteCond %{REQUEST_URI} !^/wp-content/uploads/investors/protect.php
        RewriteRule ^(.*)$ /wp-content/uploads/investors/protect.php?file=$1 [L]
    </Directory>

 #   # Directory specific for protecting uploads/faction-docs
 #   <Directory "/var/www/investors/wp-content/uploads/faction-docs">
 #       Options -Indexes +FollowSymLinks
 #       AllowOverride None
 #       Order allow,deny
 #       allow from all
#
#        RewriteEngine On
#        RewriteBase /wp-content/uploads/faction-docs/
#        RewriteCond %{REQUEST_FILENAME} -f
#        RewriteCond %{REQUEST_URI} !^/wp-content/uploads/faction-docs/protect_docs.php
#        RewriteRule ^(.*)$ /wp-content/uploads/faction-docs/protect_docs.php?file=$1 [L]
#    </Directory>

    ErrorLog ${APACHE_LOG_DIR}/error.log
    CustomLog ${APACHE_LOG_DIR}/access.log combined

    SSLCertificateFile /etc/letsencrypt/live/investors.factionnetworks.com/fullchain.pem
    SSLCertificateKeyFile /etc/letsencrypt/live/investors.factionnetworks.com/privkey.pem
    Include /etc/letsencrypt/options-ssl-apache.conf
</VirtualHost>
</IfModule>

