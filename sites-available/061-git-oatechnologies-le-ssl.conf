<IfModule mod_ssl.c>
<VirtualHost *:443>
ServerName git.oatechnologies.us

ServerAdmin webmaster@localhost
DocumentRoot /var/www/git_oatechnologies
        <Directory "/var/www/git_oatechnologies"> 
         Options FollowSymLinks
         AllowOverride All  
         Order allow,deny
         Allow from all
       </Directory> 

ErrorLog ${APACHE_LOG_DIR}/git_oatechnologies_error.log
CustomLog ${APACHE_LOG_DIR}/git_oatechnologies_access.log combined

Include /etc/letsencrypt/options-ssl-apache.conf
SSLCertificateFile /etc/letsencrypt/live/git.oatechnologies.us/fullchain.pem
SSLCertificateKeyFile /etc/letsencrypt/live/git.oatechnologies.us/privkey.pem
</VirtualHost>
</IfModule>
