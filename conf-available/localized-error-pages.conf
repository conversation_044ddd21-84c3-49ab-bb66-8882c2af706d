# Customizable error responses come in three flavors:
# 1) plain text
# 2) local redirects
# 3) external redirects
#
# Some examples:
#ErrorDocument 500 "The server made a boo boo."
#ErrorDocument 404 /missing.html
#ErrorDocument 404 "/cgi-bin/missing_handler.pl"
#ErrorDocument 402 http://www.example.com/subscription_info.html
#

#
# Putting this all together, we can internationalize error responses.
#
# We use Alias to redirect any /error/HTTP_<error>.html.var response to
# our collection of by-error message multi-language collections.  We use
# includes to substitute the appropriate text.
#
# You can modify the messages' appearance without changing any of the
# default HTTP_<error>.html.var files by adding the line:
#
#Alias /error/include/ "/your/include/path/"
#
# which allows you to create your own set of files by starting with the
# /usr/share/apache2/error/include/ files and copying them to /your/include/path/,
# even on a per-VirtualHost basis.  If you include the Alias in the global server
# context, is has to come _before_ the 'Alias /error/ ...' line.
#
# The default include files will display your Apache version number and your
# ServerAdmin email address regardless of the setting of ServerSignature.
#
# WARNING: The configuration below will NOT work out of the box if you have a
#		  SetHandler directive in a <Location /> context somewhere. Adding
#		  the following three lines AFTER the <Location /> context should
#		  make it work in most cases:
#		  <Location /error/>
#			 SetHandler none
#		  </Location>
#
# The internationalized error documents require mod_alias, mod_include
# and mod_negotiation.  To activate them, uncomment the following 37 lines.

#<IfModule mod_negotiation.c>
#	<IfModule mod_include.c>
#		<IfModule mod_alias.c>
#
#			Alias /error/ "/usr/share/apache2/error/"
#
#			<Directory "/usr/share/apache2/error">
#				Options IncludesNoExec
#				AddOutputFilter Includes html
#				AddHandler type-map var
#				Order allow,deny
#				Allow from all
#				LanguagePriority en cs de es fr it nl sv pt-br ro
#				ForceLanguagePriority Prefer Fallback
#			</Directory>
#
#			ErrorDocument 400 /error/HTTP_BAD_REQUEST.html.var
#			ErrorDocument 401 /error/HTTP_UNAUTHORIZED.html.var
#			ErrorDocument 403 /error/HTTP_FORBIDDEN.html.var
#			ErrorDocument 404 /error/HTTP_NOT_FOUND.html.var
#			ErrorDocument 405 /error/HTTP_METHOD_NOT_ALLOWED.html.var
#			ErrorDocument 408 /error/HTTP_REQUEST_TIME_OUT.html.var
#			ErrorDocument 410 /error/HTTP_GONE.html.var
#			ErrorDocument 411 /error/HTTP_LENGTH_REQUIRED.html.var
#			ErrorDocument 412 /error/HTTP_PRECONDITION_FAILED.html.var
#			ErrorDocument 413 /error/HTTP_REQUEST_ENTITY_TOO_LARGE.html.var
#			ErrorDocument 414 /error/HTTP_REQUEST_URI_TOO_LARGE.html.var
#			ErrorDocument 415 /error/HTTP_UNSUPPORTED_MEDIA_TYPE.html.var
#			ErrorDocument 500 /error/HTTP_INTERNAL_SERVER_ERROR.html.var
#			ErrorDocument 501 /error/HTTP_NOT_IMPLEMENTED.html.var
#			ErrorDocument 502 /error/HTTP_BAD_GATEWAY.html.var
#			ErrorDocument 503 /error/HTTP_SERVICE_UNAVAILABLE.html.var
#			ErrorDocument 506 /error/HTTP_VARIANT_ALSO_VARIES.html.var
#		</IfModule>
#	</IfModule>
#</IfModule>

# vim: syntax=apache ts=4 sw=4 sts=4 sr noet
