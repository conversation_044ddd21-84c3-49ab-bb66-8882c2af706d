<IfModule reqtimeout_module>

	# mod_reqtimeout limits the time waiting on the client to prevent an
	# attacker from causing a denial of service by opening many connections
	# but not sending requests. This file tries to give a sensible default
	# configuration, but it may be necessary to tune the timeout values to
	# the actual situation. Note that it is also possible to configure
	# mod_reqtimeout per virtual host.


	# Wait max 20 seconds for the first byte of the request line+headers
	# From then, require a minimum data rate of 500 bytes/s, but don't
	# wait longer than 40 seconds in total.
	# Note: Lower timeouts may make sense on non-ssl virtual hosts but can
	# cause problem with ssl enabled virtual hosts: This timeout includes
	# the time a browser may need to fetch the CRL for the certificate. If
	# the CRL server is not reachable, it may take more than 10 seconds
	# until the browser gives up.
	RequestReadTimeout header=20-40,minrate=500

	# Wait max 10 seconds for the first byte of the request body (if any)
	# From then, require a minimum data rate of 500 bytes/s
	RequestReadTimeout body=10,minrate=500

</IfModule>

# vim: syntax=apache ts=4 sw=4 sts=4 sr noet
