<IfModule mod_cache_disk.c>

	# cache cleaning is done by htcacheclean, which can be configured in
	# /etc/default/apache2
	#
	# For further information, see the comments in that file,
	# /usr/share/doc/apache2/README.Debian, and the htcacheclean(8)
	# man page.

	# This path must be the same as the one in /etc/default/apache2
	CacheRoot /var/cache/apache2/mod_cache_disk

	# This will also cache local documents. It usually makes more sense to
	# put this into the configuration for just one virtual host.
	#CacheEnable disk /


    # The result of CacheDirLevels * CacheDirLength must not be higher than
    # 20. Moreover, pay attention on file system limits. Some file systems
    # do not support more than a certain number of inodes and
    # subdirectories (e.g. 32000 for ext3)
    CacheDirLevels 2
    CacheDirLength 1

</IfModule>

# vim: syntax=apache ts=4 sw=4 sts=4 sr noet
