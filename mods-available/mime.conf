<IfModule mod_mime.c>

	#
	# TypesConfig points to the file containing the list of mappings from
	# filename extension to MIME-type.
	#
	TypesConfig /etc/mime.types

	#
	# AddType allows you to add to or override the MIME configuration
	# file mime.types for specific file types.
	#
	#AddType application/x-gzip .tgz
	#
	# AddEncoding allows you to have certain browsers uncompress
	# information on the fly. Note: Not all browsers support this.
	# Despite the name similarity, the following Add* directives have
	# nothing to do with the FancyIndexing customization directives above.
	#
	#AddEncoding x-compress .Z
	#AddEncoding x-gzip .gz .tgz
	#AddEncoding x-bzip2 .bz2
	#
	# If the AddEncoding directives above are commented-out, then you
	# probably should define those extensions to indicate media types:
	#
	AddType application/x-compress .Z
	AddType application/x-gzip .gz .tgz
	AddType application/x-bzip2 .bz2

	#
	# DefaultLanguage and AddLanguage allows you to specify the language of 
	# a document. You can then use content negotiation to give a browser a 
	# file in a language the user can understand.
	#
	# Specify a default language. This means that all data
	# going out without a specific language tag (see below) will 
	# be marked with this one. You probably do NOT want to set
	# this unless you are sure it is correct for all cases.
	#
	# * It is generally better to not mark a page as 
	# * being a certain language than marking it with the wrong
	# * language!
	#
	# DefaultLanguage nl
	#
	# Note 1: The suffix does not have to be the same as the language
	# keyword --- those with documents in Polish (whose net-standard
	# language code is pl) may wish to use "AddLanguage pl .po" to
	# avoid the ambiguity with the common suffix for perl scripts.
	#
	# Note 2: The example entries below illustrate that in some cases 
	# the two character 'Language' abbreviation is not identical to 
	# the two character 'Country' code for its country,
	# E.g. 'Danmark/dk' versus 'Danish/da'.
	#
	# Note 3: In the case of 'ltz' we violate the RFC by using a three char
	# specifier. There is 'work in progress' to fix this and get
	# the reference data for rfc1766 cleaned up.
	#
	# Catalan (ca) - Croatian (hr) - Czech (cs) - Danish (da) - Dutch (nl)
	# English (en) - Esperanto (eo) - Estonian (et) - French (fr) - German (de)
	# Greek-Modern (el) - Hebrew (he) - Italian (it) - Japanese (ja)
	# Korean (ko) - Luxembourgeois* (ltz) - Norwegian Nynorsk (nn)
	# Norwegian (no) - Polish (pl) - Portugese (pt)
	# Brazilian Portuguese (pt-BR) - Russian (ru) - Swedish (sv)
	# Simplified Chinese (zh-CN) - Spanish (es) - Traditional Chinese (zh-TW)
	#
	AddLanguage am .amh
	AddLanguage ar .ara
	AddLanguage be .be
	AddLanguage bg .bg
	AddLanguage bn .bn
	AddLanguage br .br
	AddLanguage bs .bs
	AddLanguage ca .ca
	AddLanguage cs .cz .cs
	AddLanguage cy .cy
	AddLanguage da .da
	AddLanguage da .dk
	AddLanguage de .de
	AddLanguage dz .dz
	AddLanguage el .el
	AddLanguage en .en
	AddLanguage eo .eo
	# es is ecmascript in /etc/mime.types
	RemoveType  es
	AddLanguage es .es
	AddLanguage et .et
	AddLanguage eu .eu
	AddLanguage fa .fa
	AddLanguage fi .fi
	AddLanguage fr .fr
	AddLanguage ga .ga
	AddLanguage gl .glg
	AddLanguage gu .gu
	AddLanguage he .he
	AddLanguage hi .hi
	AddLanguage hr .hr
	AddLanguage hu .hu
	AddLanguage hy .hy
	AddLanguage id .id
	AddLanguage is .is
	AddLanguage it .it
	AddLanguage ja .ja
	AddLanguage ka .ka
	AddLanguage kk .kk
	AddLanguage km .km
	AddLanguage kn .kn
	AddLanguage ko .ko
	AddLanguage ku .ku
	AddLanguage lo .lo
	AddLanguage lt .lt
	AddLanguage ltz .ltz
	AddLanguage lv .lv
	AddLanguage mg .mg
	AddLanguage mk .mk
	AddLanguage ml .ml
	AddLanguage mr .mr
	AddLanguage ms .msa
	AddLanguage nb .nob
	AddLanguage ne .ne
	AddLanguage nl .nl
	AddLanguage nn .nn
	AddLanguage no .no
	AddLanguage pa .pa
	AddLanguage pl .po
	AddLanguage pt-BR .pt-br
	AddLanguage pt .pt
	AddLanguage ro .ro
	AddLanguage ru .ru
	AddLanguage sa .sa
	AddLanguage se .se
	AddLanguage si .si
	AddLanguage sk .sk
	AddLanguage sl .sl
	AddLanguage sq .sq
	AddLanguage sr .sr
	AddLanguage sv .sv
	AddLanguage ta .ta
	AddLanguage te .te
	AddLanguage th .th
	AddLanguage tl .tl
	RemoveType  tr
	# tr is troff in /etc/mime.types
	AddLanguage tr .tr
	AddLanguage uk .uk
	AddLanguage ur .ur
	AddLanguage vi .vi
	AddLanguage wo .wo
	AddLanguage xh .xh
	AddLanguage zh-CN .zh-cn
	AddLanguage zh-TW .zh-tw

	#
	# Commonly used filename extensions to character sets. You probably
	# want to avoid clashes with the language extensions, unless you
	# are good at carefully testing your setup after each change.
	# See http://www.iana.org/assignments/character-sets for the
	# official list of charset names and their respective RFCs.
	#
	AddCharset us-ascii	.ascii .us-ascii
	AddCharset ISO-8859-1  .iso8859-1  .latin1
	AddCharset ISO-8859-2  .iso8859-2  .latin2 .cen
	AddCharset ISO-8859-3  .iso8859-3  .latin3
	AddCharset ISO-8859-4  .iso8859-4  .latin4
	AddCharset ISO-8859-5  .iso8859-5  .cyr .iso-ru
	AddCharset ISO-8859-6  .iso8859-6  .arb .arabic
	AddCharset ISO-8859-7  .iso8859-7  .grk .greek
	AddCharset ISO-8859-8  .iso8859-8  .heb .hebrew
	AddCharset ISO-8859-9  .iso8859-9  .latin5 .trk
	AddCharset ISO-8859-10  .iso8859-10  .latin6
	AddCharset ISO-8859-13  .iso8859-13
	AddCharset ISO-8859-14  .iso8859-14  .latin8
	AddCharset ISO-8859-15  .iso8859-15  .latin9
	AddCharset ISO-8859-16  .iso8859-16  .latin10
	AddCharset ISO-2022-JP .iso2022-jp .jis
	AddCharset ISO-2022-KR .iso2022-kr .kis
	AddCharset ISO-2022-CN .iso2022-cn .cis
	AddCharset Big5		.Big5	   .big5 .b5
	AddCharset cn-Big5	 .cn-big5
	# For russian, more than one charset is used (depends on client, mostly):
	AddCharset WINDOWS-1251 .cp-1251   .win-1251
	AddCharset CP866	   .cp866
	AddCharset KOI8	  .koi8
	AddCharset KOI8-E	  .koi8-e
	AddCharset KOI8-r	  .koi8-r .koi8-ru
	AddCharset KOI8-U	  .koi8-u
	AddCharset KOI8-ru	 .koi8-uk .ua
	AddCharset ISO-10646-UCS-2 .ucs2
	AddCharset ISO-10646-UCS-4 .ucs4
	AddCharset UTF-7	   .utf7
	AddCharset UTF-8	   .utf8
	AddCharset UTF-16	  .utf16
	AddCharset UTF-16BE	.utf16be
	AddCharset UTF-16LE	.utf16le
	AddCharset UTF-32	  .utf32
	AddCharset UTF-32BE	.utf32be
	AddCharset UTF-32LE	.utf32le
	AddCharset euc-cn	  .euc-cn
	AddCharset euc-gb	  .euc-gb
	AddCharset euc-jp	  .euc-jp
	AddCharset euc-kr	  .euc-kr
	#Not sure how euc-tw got in - IANA doesn't list it???
	AddCharset EUC-TW	  .euc-tw
	AddCharset gb2312	  .gb2312 .gb
	AddCharset iso-10646-ucs-2 .ucs-2 .iso-10646-ucs-2
	AddCharset iso-10646-ucs-4 .ucs-4 .iso-10646-ucs-4
	AddCharset shift_jis   .shift_jis .sjis
	AddCharset BRF		 .brf

	#
	# AddHandler allows you to map certain file extensions to "handlers":
	# actions unrelated to filetype. These can be either built into the server
	# or added with the Action directive (see below)
	#
	# To use CGI scripts outside of ScriptAliased directories:
	# (You will also need to add "ExecCGI" to the "Options" directive.)
	#
	#AddHandler cgi-script .cgi

	#
	# For files that include their own HTTP headers:
	#
	#AddHandler send-as-is asis

	#
	# For server-parsed imagemap files:
	#
	#AddHandler imap-file map

	#
	# For type maps (negotiated resources):
	# (This is enabled by default to allow the Apache "It Worked" page
	#  to be distributed in multiple languages.)
	#
	AddHandler type-map var

	#
	# Filters allow you to process content before it is sent to the client.
	#
	# To parse .shtml files for server-side includes (SSI):
	# (You will also need to add "Includes" to the "Options" directive.)
	#
	AddType text/html .shtml
<IfModule mod_include.c>
	AddOutputFilter INCLUDES .shtml
</IfModule>

</IfModule>

# vim: syntax=apache ts=4 sw=4 sts=4 sr noet
