<IfModule mod_proxy.c>

	# If you want to use apache2 as a forward proxy, uncomment the
	# 'ProxyRequests On' line and the <Proxy *> block below.
	# WARNING: Be careful to restrict access inside the <Proxy *> block.
	# Open proxy servers are dangerous both to your network and to the
	# Internet at large.
	#
	# If you only want to use apache2 as a reverse proxy/gateway in
	# front of some web application server, you DON'T need
	# 'ProxyRequests On'.

	#ProxyRequests On
	#<Proxy *>
	#   AddDefaultCharset off
	#   Require all denied
	#   #Require local
	#</Proxy>

	# Enable/disable the handling of HTTP/1.1 "Via:" headers.
	# ("Full" adds the server version; "Block" removes all outgoing Via: headers)
	# Set to one of: Off | On | Full | Block
	#ProxyVia Off

</IfModule>

# vim: syntax=apache ts=4 sw=4 sts=4 sr noet
