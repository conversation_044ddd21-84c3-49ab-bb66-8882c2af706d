<IfModule mod_setenvif.c>

	#
	# The following directives modify normal HTTP response behavior to
	# handle known problems with browser implementations.
	#
	BrowserMatch "Mozilla/2" nokeepalive
	BrowserMatch "MSIE 4\.0b2;" nokeepalive downgrade-1.0 force-response-1.0
	BrowserMatch "RealPlayer 4\.0" force-response-1.0
	BrowserMatch "Java/1\.0" force-response-1.0
	BrowserMatch "JDK/1\.0" force-response-1.0

	#
	# The following directive disables redirects on non-GET requests for
	# a directory that does not include the trailing slash.  This fixes a
	# problem with Microsoft WebFolders which does not appropriately handle
	# redirects for folders with DAV methods.
	# Same deal with Apple's DAV filesystem and Gnome VFS support for DAV.
	#
	BrowserMatch "Microsoft Data Access Internet Publishing Provider" redirect-carefully
	BrowserMatch "MS FrontPage" redirect-carefully
	BrowserMatch "^WebDrive" redirect-carefully
	BrowserMatch "^WebDAVFS/1.[012]" redirect-carefully
	BrowserMatch "^gnome-vfs/1.0" redirect-carefully
	BrowserMatch "^gvfs/1" redirect-carefully
	BrowserMatch "^XML Spy" redirect-carefully
	BrowserMatch "^Dreamweaver-WebDAV-SCM1" redirect-carefully
	BrowserMatch " Konqueror/4" redirect-carefully
	BrowserMatch " Konqueror/5" redirect-carefully
	BrowserMatch " dolphin/" redirect-carefully

</IfModule>

# vim: syntax=apache ts=4 sw=4 sts=4 sr noet
