
# mod_http2 doesn't work with mpm_prefork
<IfModule !mpm_prefork>
    Protocols h2 h2c http/1.1

    # # HTTP/2 push configuration
    # 
    # H2Push          on
    # 
    # # Default Priority Rule
    # 
    # H2PushPriority * After 16
    # 
    # # More complex ruleset:
    # 
    # H2PushPriority  *                       after
    # H2PushPriority  text/css                before
    # H2PushPriority  image/jpeg              after   32
    # H2PushPriority  image/png               after   32
    # H2PushPriority  application/javascript  interleaved
    # 
    # # Configure some stylesheet and script to be pushed by the webserver
    # 
    # <FilesMatch "\.html$">
    #     Header add Link "</style.css>; rel=preload; as=style"
    #     Header add Link "</script.js>; rel=preload; as=script"
    # </FilesMatch>
    # Since mod_http2 doesn't support the mod_logio module (which provide the %O format),
    # you may want to change your LogFormat directive as follow:
    #
    # LogFormat "%v:%p %h %l %u %t \"%r\" %>s %B \"%{Referer}i\" \"%{User-Agent}i\"" vhost_combined
    # LogFormat "%h %l %u %t \"%r\" %>s %B \"%{Referer}i\" \"%{User-Agent}i\"" combined
    # LogFormat "%h %l %u %t \"%r\" %>s %B" common
</IfModule>
