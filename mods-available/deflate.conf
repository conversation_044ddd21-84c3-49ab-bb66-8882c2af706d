<IfModule mod_deflate.c>
	<IfModule mod_filter.c>
		AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css text/javascript
		AddOutputFilterByType DEFLATE application/x-javascript application/javascript application/ecmascript
		AddOutputFilterByType DEFLATE application/rss+xml
		AddOutputFilterByType DEFLATE application/wasm
		AddOutputFilterByType DEFLATE application/xml
	</IfModule>
</IfModule>

# vim: syntax=apache ts=4 sw=4 sts=4 sr noet
