<IfModule mod_status.c>
	# Allow server status reports generated by mod_status,
	# with the URL of http://servername/server-status
	# Uncomment and change the "*********/24" to allow access from other hosts.

	<Location /server-status>
		SetHandler server-status
		Require local
		#Require ip *********/24
	</Location>

	# Keep track of extended status information for each request
	ExtendedStatus On

	# Determine if mod_status displays the first 63 characters of a request or
	# the last 63, assuming the request itself is greater than 63 chars.
	# Default: Off
	#SeeRequestTail On


	<IfModule mod_proxy.c>
		# Show Proxy LoadBalancer status in mod_status
		ProxyStatus On
	</IfModule>


</IfModule>

# vim: syntax=apache ts=4 sw=4 sts=4 sr noet
