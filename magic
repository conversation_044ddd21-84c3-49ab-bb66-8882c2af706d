# Magic data for mod_mime_magic (originally for file(1) command)
#
# The format is 4-5 columns:
#    Column #1: byte number to begin checking from, ">" indicates continuation
#    Column #2: type of data to match
#    Column #3: contents of data to match
#    Column #4: MIME type of result
#    Column #5: MIME encoding of result (optional)

#------------------------------------------------------------------------------
# Localstuff:  file(1) magic for locally observed files
# Add any locally observed files here.

# Real Audio (Magic .ra\0375)
0	belong		0x2e7261fd	audio/x-pn-realaudio
0	string		.RMF		application/vnd.rn-realmedia

#video/x-pn-realvideo
#video/vnd.rn-realvideo
#application/vnd.rn-realmedia
#	sigh, there are many mimes for that but the above are the most common.

# Taken from magic, converted to magic.mime
# mime types according to http://www.geocities.com/nevilo/mod.htm:
#	audio/it	.it
#	audio/x-zipped-it	.itz
#	audio/xm	fasttracker modules
#	audio/x-s3m	screamtracker modules
#	audio/s3m	screamtracker modules
#	audio/x-zipped-mod	mdz
#	audio/mod	mod
#	audio/x-mod	All modules (mod, s3m, 669, mtm, med, xm, it, mdz, stm, itz, xmz, s3z)

# Taken from loader code from mikmod version 2.14
# by Steve McIntyre (<EMAIL>)
# <<EMAIL>> added title printing on 2003-06-24
0	string	MAS_UTrack_V00
>14	string	>/0		audio/x-mod
#audio/x-tracker-module

#0	string	UN05		MikMod UNI format module sound data

0	string	Extended\ Module: audio/x-mod
#audio/x-tracker-module
##>17	string	>\0		Title: "%s"

21	string/c	\!SCREAM!	audio/x-mod
#audio/x-screamtracker-module
21	string	BMOD2STM	audio/x-mod
#audio/x-screamtracker-module
1080	string	M.K.		audio/x-mod
#audio/x-protracker-module
#>0	string	>\0		Title: "%s"
1080	string	M!K!		audio/x-mod
#audio/x-protracker-module
#>0	string	>\0		Title: "%s"
1080	string	FLT4		audio/x-mod
#audio/x-startracker-module
#>0	string	>\0		Title: "%s"
1080	string	FLT8		audio/x-mod
#audio/x-startracker-module
#>0	string	>\0		Title: "%s"
1080	string	4CHN		audio/x-mod
#audio/x-fasttracker-module
#>0	string	>\0		Title: "%s"
1080	string	6CHN		audio/x-mod
#audio/x-fasttracker-module
#>0	string	>\0		Title: "%s"
1080	string	8CHN		audio/x-mod
#audio/x-fasttracker-module
#>0	string	>\0		Title: "%s"
1080	string	CD81		audio/x-mod
#audio/x-oktalyzer-tracker-module
#>0	string	>\0		Title: "%s"
1080	string	OKTA		audio/x-mod
#audio/x-oktalyzer-tracker-module
#>0	string	>\0		Title: "%s"
# Not good enough.
#1082	string	CH
#>1080	string	>/0		%.2s-channel Fasttracker "oktalyzer" module sound data
1080	string	16CN		audio/x-mod
#audio/x-taketracker-module
#>0	string	>\0		Title: "%s"
1080	string	32CN		audio/x-mod
#audio/x-taketracker-module
#>0	string	>\0		Title: "%s"

# Impuse tracker module (it)
0	string		IMPM		audio/x-mod
#>4	string		>\0		"%s"
#>40	leshort		!0		compatible w/ITv%x
#>42	leshort		!0		created w/ITv%x

#------------------------------------------------------------------------------
# end local stuff
#------------------------------------------------------------------------------

# xml based formats!

# svg

0	string		\<?xml
#			text/xml
>38	string		\<\!DOCTYPE\040svg	image/svg+xml


# xml
0	string		\<?xml			text/xml


#------------------------------------------------------------------------------
# Java

0	short		0xcafe
>2	short		0xbabe		application/java

#------------------------------------------------------------------------------
# audio:  file(1) magic for sound formats
#
# <AUTHOR> <EMAIL>,
#

# Sun/NeXT audio data
0	string		.snd
>12	belong		1		audio/basic
>12	belong		2		audio/basic
>12	belong		3		audio/basic
>12	belong		4		audio/basic
>12	belong		5		audio/basic
>12	belong		6		audio/basic
>12	belong		7		audio/basic

>12	belong		23		audio/x-adpcm

# DEC systems (e.g. DECstation 5000) use a variant of the Sun/NeXT format
# that uses little-endian encoding and has a different magic number
# (0x0064732E in little-endian encoding).
0	lelong		0x0064732E	
>12	lelong		1		audio/x-dec-basic
>12	lelong		2		audio/x-dec-basic
>12	lelong		3		audio/x-dec-basic
>12	lelong		4		audio/x-dec-basic
>12	lelong		5		audio/x-dec-basic
>12	lelong		6		audio/x-dec-basic
>12	lelong		7		audio/x-dec-basic
#                                       compressed (G.721 ADPCM)
>12	lelong		23		audio/x-dec-adpcm

# Bytes 0-3 of AIFF, AIFF-C, & 8SVX audio files are "FORM"
#					AIFF audio data
8	string		AIFF		audio/x-aiff	
#					AIFF-C audio data
8	string		AIFC		audio/x-aiff	
#					IFF/8SVX audio data
8	string		8SVX		audio/x-aiff	



# Creative Labs AUDIO stuff
#					Standard MIDI data
0	string	MThd			audio/unknown	
#>9 	byte	>0			(format %d)
#>11	byte	>1			using %d channels
#					Creative Music (CMF) data
0	string	CTMF			audio/unknown	
#					SoundBlaster instrument data
0	string	SBI			audio/unknown	
#					Creative Labs voice data
0	string	Creative\ Voice\ File	audio/unknown	
## is this next line right?  it came this way...
#>19	byte	0x1A
#>23	byte	>0			- version %d
#>22	byte	>0			\b.%d

# [GRR 950115:  is this also Creative Labs?  Guessing that first line
#  should be string instead of unknown-endian long...]
#0	long		0x4e54524b	MultiTrack sound data
#0	string		NTRK		MultiTrack sound data
#>4	long		x		- version %ld

# Microsoft WAVE format (*.wav)
# [GRR 950115:  probably all of the shorts and longs should be leshort/lelong]
#					Microsoft RIFF
0	string		RIFF
#					- WAVE format
>8	string		WAVE		audio/x-wav
>8	string/B	AVI		video/x-msvideo
#
>8 	string		CDRA		image/x-coreldraw

# AAC (aka MPEG-2 NBC)
0       beshort&0xfff6    0xfff0          audio/X-HX-AAC-ADTS
0       string          ADIF            audio/X-HX-AAC-ADIF
0       beshort&0xffe0  0x56e0          audio/MP4A-LATM
0       beshort         0x4De1          audio/MP4A-LATM

# MPEG Layer 3 sound files
0       beshort&0xfffe  =0xfffa         audio/mpeg
#MP3 with ID3 tag
0	string		ID3		audio/mpeg
# Ogg/Vorbis
0	string		OggS		application/ogg

#------------------------------------------------------------------------------
# c-lang:  file(1) magic for C programs or various scripts
#

# XPM icons (Greg Roelofs, <EMAIL>)
# ideally should go into "images", but entries below would tag XPM as C source
0	string		/*\ XPM		image/x-xpmi 7bit

# 3DS (3d Studio files)
#16	beshort		0x3d3d		image/x-3ds

# this first will upset you if you're a PL/1 shop... (are there any left?)
# in which case rm it; ascmagic will catch real C programs
#					C or REXX program text
#0	string		/*		text/x-c
#					C++ program text
#0	string		//		text/x-c++

#------------------------------------------------------------------------------
# commands:  file(1) magic for various shells and interpreters
#
#0       string          :\ shell archive or commands for antique kernel text
0       string          #!/bin/sh               application/x-shellscript
0       string          #!\ /bin/sh             application/x-shellscript
0       string          #!/bin/csh              application/x-shellscript
0       string          #!\ /bin/csh            application/x-shellscript
# korn shell magic, sent by George Wu, <EMAIL>
0       string          #!/bin/ksh              application/x-shellscript
0       string          #!\ /bin/ksh            application/x-shellscript
0       string          #!/bin/tcsh             application/x-shellscript
0       string          #!\ /bin/tcsh           application/x-shellscript
0       string          #!/usr/local/tcsh       application/x-shellscript
0       string          #!\ /usr/local/tcsh     application/x-shellscript
0       string          #!/usr/local/bin/tcsh   application/x-shellscript
0       string          #!\ /usr/local/bin/tcsh application/x-shellscript
# bash shell magic, from Peter Tobias (<EMAIL>)
0       string          #!/bin/bash     		application/x-shellscript
0       string          #!\ /bin/bash           application/x-shellscript
0       string          #!/usr/local/bin/bash   application/x-shellscript
0       string          #!\ /usr/local/bin/bash application/x-shellscript

#
# zsh/ash/ae/nawk/gawk <NAME_EMAIL> (Cameron Simpson)
0       string          #!/bin/zsh	        application/x-shellscript
0       string          #!/usr/bin/zsh	        application/x-shellscript
0       string          #!/usr/local/bin/zsh    application/x-shellscript
0       string          #!\ /usr/local/bin/zsh  application/x-shellscript
0       string          #!/usr/local/bin/ash    application/x-shellscript
0       string          #!\ /usr/local/bin/ash  application/x-shellscript
#0       string          #!/usr/local/bin/ae     Neil Brown's ae
#0       string          #!\ /usr/local/bin/ae   Neil Brown's ae
0       string          #!/bin/nawk             application/x-nawk
0       string          #!\ /bin/nawk           application/x-nawk
0       string          #!/usr/bin/nawk         application/x-nawk
0       string          #!\ /usr/bin/nawk       application/x-nawk
0       string          #!/usr/local/bin/nawk   application/x-nawk
0       string          #!\ /usr/local/bin/nawk application/x-nawk
0       string          #!/bin/gawk             application/x-gawk
0       string          #!\ /bin/gawk           application/x-gawk
0       string          #!/usr/bin/gawk         application/x-gawk
0       string          #!\ /usr/bin/gawk       application/x-gawk
0       string          #!/usr/local/bin/gawk   application/x-gawk
0       string          #!\ /usr/local/bin/gawk application/x-gawk
#
0       string          #!/bin/awk              application/x-awk
0       string          #!\ /bin/awk            application/x-awk
0       string          #!/usr/bin/awk          application/x-awk
0       string          #!\ /usr/bin/awk        application/x-awk
# update to distinguish from *.vcf files by Joerg Jenderek: joerg dot jenderek at web dot de
#0	regex		BEGIN[[:space:]]*[{]	application/x-awk

# For Larry Wall's perl language.  The ``eval'' line recognizes an
# outrageously clever hack for USG systems.
# <AUTHOR> <EMAIL>
0       string          #!/bin/perl                     application/x-perl
0       string          #!\ /bin/perl                   application/x-perl
0       string          eval\ "exec\ /bin/perl          application/x-perl
0       string          #!/usr/bin/perl                 application/x-perl
0       string          #!\ /usr/bin/perl               application/x-perl
0       string          eval\ "exec\ /usr/bin/perl      application/x-perl
0       string          #!/usr/local/bin/perl           application/x-perl
0       string          #!\ /usr/local/bin/perl         application/x-perl
0       string          eval\ "exec\ /usr/local/bin/perl application/x-perl

#------------------------------------------------------------------------------
# compress:  file(1) magic for pure-compression formats (no archives)
#
# compress, gzip, pack, compact, huf, squeeze, crunch, freeze, yabba, whap, etc.
#
# Formats for various forms of compressed data
# Formats for "compress" proper have been moved into "compress.c",
# because it tries to uncompress it to figure out what's inside.

# standard unix compress
#0	string		\037\235	application/x-compress

# gzip (GNU zip, not to be confused with [Info-ZIP/PKWARE] zip archiver)
#0       string          \037\213        application/x-gzip

0		string			PK\003\004		application/x-zip

# RAR archiver (Greg Roelofs, <EMAIL>)
0	string		Rar!		application/x-rar

# According to gzip.h, this is the correct byte order for packed data.
0	string		\037\036	application/octet-stream
#
# This magic number is byte-order-independent.
#
0	short		017437		application/octet-stream

# XXX - why *two* entries for "compacted data", one of which is
# byte-order independent, and one of which is byte-order dependent?
#
# compacted data
0	short		0x1fff		application/octet-stream
0	string		\377\037	application/octet-stream
# huf output
0	short		0145405		application/octet-stream

# Squeeze and Crunch...
# These numbers were gleaned from the Unix versions of the programs to
# handle these formats.  Note that I can only uncrunch, not crunch, and
# I didn't have a crunched file handy, so the crunch number is untested.
#				Keith Waclena <<EMAIL>>
#0	leshort		0x76FF		squeezed data (CP/M, DOS)
#0	leshort		0x76FE		crunched data (CP/M, DOS)

# Freeze
#0	string		\037\237	Frozen file 2.1
#0	string		\037\236	Frozen file 1.0 (or gzip 0.5)

# lzh?
#0	string		\037\240	LZH compressed data

257	string		ustar\0		application/x-tar	posix
257	string		ustar\040\040\0		application/x-tar	gnu

0	short		070707		application/x-cpio
0	short		0143561		application/x-cpio	swapped

0	string		=<ar>		application/x-archive
0	string		\!<arch>	application/x-archive
>8	string		debian		application/x-debian-package

#------------------------------------------------------------------------------
#
# RPM: file(1) magic for Red Hat Packages   Erik Troan (<EMAIL>)
#
0       beshort         0xedab
>2      beshort         0xeedb          application/x-rpm

0	lelong&0x8080ffff	0x0000081a	application/x-arc	lzw
0	lelong&0x8080ffff	0x0000091a	application/x-arc	squashed
0	lelong&0x8080ffff	0x0000021a	application/x-arc	uncompressed
0	lelong&0x8080ffff	0x0000031a	application/x-arc	packed
0	lelong&0x8080ffff	0x0000041a	application/x-arc	squeezed
0	lelong&0x8080ffff	0x0000061a	application/x-arc	crunched

0	leshort	0xea60	application/x-arj

# LHARC/LHA archiver (Greg Roelofs, <EMAIL>)
2	string	-lh0-	application/x-lharc	lh0
2	string	-lh1-	application/x-lharc	lh1
2	string	-lz4-	application/x-lharc	lz4
2	string	-lz5-	application/x-lharc	lz5
#	[never seen any but the last; -lh4- reported in comp.compression:]
2	string	-lzs-	application/x-lha	lzs
2	string	-lh\ -	application/x-lha	lh
2	string	-lhd-	application/x-lha	lhd
2	string	-lh2-	application/x-lha	lh2
2	string	-lh3-	application/x-lha	lh3
2	string	-lh4-	application/x-lha	lh4
2	string	-lh5-	application/x-lha	lh5
2	string	-lh6-	application/x-lha	lh6
2	string	-lh7-	application/x-lha	lh7
# Shell archives
10	string	#\ This\ is\ a\ shell\ archive	application/octet-stream	x-shell

#------------------------------------------------------------------------------
# frame:  file(1) magic for FrameMaker files
#
# This stuff came on a FrameMaker demo tape, most of which is
# copyright, but this file is "published" as witness the following:
#
0	string		\<MakerFile	application/x-frame
0	string		\<MIFFile	application/x-frame
0	string		\<MakerDictionary	application/x-frame
0	string		\<MakerScreenFon	application/x-frame
0	string		\<MML		application/x-frame
0	string		\<Book		application/x-frame
0	string		\<Maker		application/x-frame

#------------------------------------------------------------------------------
# html:  file(1) magic for HTML (HyperText Markup Language) docs
#
# <AUTHOR> <EMAIL>
#
0	string/cB	\<!DOCTYPE\ html	text/html
0	string/cb	\<head	text/html
0	string/cb	\<title	text/html
0       string/bc	\<html	text/html
0	string		\<!--	text/html
0	string/c	\<h1	text/html

0	string		\<?xml			text/xml

#------------------------------------------------------------------------------
# images:  file(1) magic for image formats (see also "c-lang" for XPM bitmaps)
#
# <NAME_EMAIL> (Jef Poskanzer),
# <NAME_EMAIL> as well as others. Jan also suggested
# merging several one- and two-line files into here.
#
# XXX - byte order for GIF and TIFF fields?
# [GRR:  TIFF allows both byte orders; GIF is probably little-endian]
#

# [GRR:  what the hell is this doing in here?]
#0	string		xbtoa		btoa'd file

# PBMPLUS
#					PBM file
0	string		P1		image/x-portable-bitmap	7bit
#					PGM file
0	string		P2		image/x-portable-greymap	7bit
#					PPM file
0	string		P3		image/x-portable-pixmap	7bit
#					PBM "rawbits" file
0	string		P4		image/x-portable-bitmap
#					PGM "rawbits" file
0	string		P5		image/x-portable-greymap
#					PPM "rawbits" file
0	string		P6		image/x-portable-pixmap

# NIFF (Navy Interchange File Format, a modification of TIFF)
# [GRR:  this *must* go before TIFF]
0	string		IIN1		image/x-niff

# TIFF and friends
#					TIFF file, big-endian
0	string		MM		image/tiff
#					TIFF file, little-endian
0	string		II		image/tiff

# possible GIF replacements; none yet released!
# (Greg Roelofs, <EMAIL>)
#
# GRR 950115:  this was mine ("Zip GIF"):
#					ZIF image (GIF+deflate alpha)
0	string		GIF94z		image/unknown
#
# GRR 950115:  this is Jeremy Wohl's Free Graphics Format (better):
#					FGF image (GIF+deflate beta)
0	string		FGF95a		image/unknown
#
# GRR 950115:  this is Thomas Boutell's Portable Bitmap Format proposal
# (best; not yet implemented):
#					PBF image (deflate compression)
0	string		PBF		image/unknown

# GIF
0	string		GIF		image/gif

# JPEG images
0	beshort		0xffd8		image/jpeg

# PC bitmaps (OS/2, Windoze BMP files)  (Greg Roelofs, <EMAIL>)
0	string		BM		image/x-ms-bmp
#>14	byte		12		(OS/2 1.x format)
#>14	byte		64		(OS/2 2.x format)
#>14	byte		40		(Windows 3.x format)
#0	string		IC		icon
#0	string		PI		pointer
#0	string		CI		color icon
#0	string		CP		color pointer
#0	string		BA		bitmap array

# CDROM Filesystems
32769    string    CD001     application/x-iso9660

# Newer StuffIt archives (<EMAIL>)
0	string		StuffIt			application/x-stuffit
#>162	string		>0			: %s

# BinHex is the Macintosh ASCII-encoded file format (see also "apple")
# Daniel Quinlan, <EMAIL>
11	string	must\ be\ converted\ with\ BinHex\ 4	application/mac-binhex40
##>41	string	x					\b, version %.3s


#------------------------------------------------------------------------------
# lisp:  file(1) magic for lisp programs
#
# various lisp types, from Daniel Quinlan (<EMAIL>)
0	string	;;			text/plain	8bit
# Emacs 18 - this is always correct, but not very magical.
0	string	\012(			application/x-elc
# Emacs 19
0	string	;ELC\023\000\000\000	application/x-elc

#------------------------------------------------------------------------------
# mail.news:  file(1) magic for mail and news
#
# There are tests to ascmagic.c to cope with mail and news.
0	string		Relay-Version: 	message/rfc822	7bit
0	string		#!\ rnews	message/rfc822	7bit
0	string		N#!\ rnews	message/rfc822	7bit
0	string		Forward\ to 	message/rfc822	7bit
0	string		Pipe\ to 	message/rfc822	7bit
0	string		Return-Path:	message/rfc822	7bit
0	string		Received:	message/rfc822
0	string		Path:		message/news	8bit
0	string		Xref:		message/news	8bit
0	string		From:		message/rfc822	7bit
0	string		Article 	message/news	8bit
#------------------------------------------------------------------------------
# msword: file(1) magic for MS Word files
#
# Contributor claims:
# Reversed-engineered MS Word magic numbers
#

0	string		\376\067\0\043			application/msword
0	string		\320\317\021\340\241\261	application/msword
0	string		\333\245-\0\0\0			application/msword



#------------------------------------------------------------------------------
# printer:  file(1) magic for printer-formatted files
#

# PostScript
0	string		%!		application/postscript
0	string		\004%!		application/postscript

# Acrobat
# (<NAME_EMAIL>)
0	string		%PDF-		application/pdf

#------------------------------------------------------------------------------
# sc:  file(1) magic for "sc" spreadsheet
#
38	string		Spreadsheet	application/x-sc

#------------------------------------------------------------------------------
# tex:  file(1) magic for TeX files
#
# XXX - needs byte-endian stuff (big-endian and little-endian DVI?)
#
# <AUTHOR> <EMAIL>

# Although we may know the offset of certain text fields in TeX DVI
# and font files, we can't use them reliably because they are not
# zero terminated. [but we do anyway, christos]
0	string		\367\002	application/x-dvi
#0	string		\367\203	TeX generic font data
#0	string		\367\131	TeX packed font data
#0	string		\367\312	TeX virtual font data
#0	string		This\ is\ TeX,	TeX transcript text	
#0	string		This\ is\ METAFONT,	METAFONT transcript text

# There is no way to detect TeX Font Metric (*.tfm) files without
# breaking them apart and reading the data.  The following patterns
# match most *.tfm files generated by METAFONT or afm2tfm.
2	string		\000\021	application/x-tex-tfm
2	string		\000\022	application/x-tex-tfm
#>34	string		>\0		(%s)

# Texinfo and GNU Info, from Daniel Quinlan (<EMAIL>)
0	string		\\input\ texinfo		text/x-texinfo
0	string		This\ is\ Info\ file	text/x-info

# correct TeX magic for Linux (and maybe more)
# from Peter Tobias (<EMAIL>)
#
0	leshort		0x02f7		application/x-dvi

# RTF - Rich Text Format
0	string		{\\rtf		text/rtf

#------------------------------------------------------------------------------
# animation:  file(1) magic for animation/movie formats
#
# animation formats, <NAME_EMAIL> (VaX#n8)
#						MPEG file
# MPEG sequences
0       belong             0x000001BA
>4      byte               &0x40          video/mp2p
>4      byte               ^0x40          video/mpeg
0       belong             0x000001BB     video/mpeg
0       belong             0x000001B0     video/mp4v-es
0       belong             0x000001B5     video/mp4v-es
0       belong             0x000001B3     video/mpv
0       belong&0xFF5FFF1F  0x47400010     video/mp2t
0       belong             0x00000001
>4      byte&0x1F          0x07           video/h264

# FLI animation format
0	leshort		0xAF11				video/fli
# FLC animation format
0	leshort		0xAF12				video/flc
#
# SGI and Apple formats
# Added ISO mimes
0	string		MOVI	      video/sgi
4	string		moov	      video/quicktime
4	string		mdat	      video/quicktime
4	string		wide	      video/quicktime
4	string		skip	      video/quicktime
4	string		free	      video/quicktime
4	string		idsc	      image/x-quicktime
4	string		idat	      image/x-quicktime
4	string		pckg	      application/x-quicktime
4	string/B	jP	      image/jp2
4	string		ftyp
>8	string		isom	      video/mp4
>8	string		mp41	      video/mp4
>8	string		mp42	      video/mp4
>8	string/B	jp2	      image/jp2
>8	string		3gp	      video/3gpp
>8      string          avc1          video/3gpp
>8	string		mmp4	      video/mp4
>8	string/B	M4A	      audio/mp4
>8	string/B	qt	      video/quicktime
# The contributor claims:
#   I couldn't find a real magic number for these, however, this
#   -appears- to work.  Note that it might catch other files, too,
#   so BE CAREFUL!
#
# Note that title and author appear in the two 20-byte chunks
# at decimal offsets 2 and 22, respectively, but they are XOR'ed with
# 255 (hex FF)! DL format SUCKS BIG ROCKS.
#
#						DL file version 1 , medium format (160x100, 4 images/screen)
0	byte		1			video/unknown
0	byte		2			video/unknown
#
# Databases
#
# GDBM magic numbers
#  Will be maintained as part of the GDBM distribution in the future.
#  <<EMAIL>>
0       belong  0x13579ace      application/x-gdbm
0       lelong  0x13579ace      application/x-gdbm
0       string  GDBM            application/x-gdbm
#
0       belong  0x061561        application/x-dbm
#
# Executables
#
0	string		\177ELF 
>16	leshort		0		application/octet-stream
>16	leshort		1		application/x-object
>16	leshort		2		application/x-executable
>16	leshort		3		application/x-sharedlib
>16	leshort		4		application/x-coredump
>16	beshort		0		application/octet-stream
>16	beshort		1		application/x-object
>16	beshort		2		application/x-executable
>16	beshort		3		application/x-sharedlib
>16	beshort		4		application/x-coredump
#
# DOS
0		string			MZ				application/x-dosexec
#
# KDE
0		string	[KDE\ Desktop\ Entry]	application/x-kdelnk
0		string	\#\ KDE\ Config\ File	application/x-kdelnk
# xmcd database file for kscd
0		string	\#\ xmcd                text/xmcd

#------------------------------------------------------------------------------
# pkgadd:  file(1) magic for SysV R4 PKG Datastreams
#
0       string          #\ PaCkAgE\ DaTaStReAm  application/x-svr4-package

#PNG Image Format
0	string		\x89PNG			image/png

# MNG Video Format, <URL:http://www.libpng.org/pub/mng/spec/>
0	string		\x8aMNG			video/x-mng
0	string		\x8aJNG			video/x-jng

#------------------------------------------------------------------------------
# Hierarchical Data Format, used to facilitate scientific data exchange
# specifications at http://hdf.ncsa.uiuc.edu/
#Hierarchical Data Format (version 4) data
0	belong		0x0e031301		application/x-hdf
#Hierarchical Data Format (version 5) data
0	string		\211HDF\r\n\032		application/x-hdf

# Adobe Photoshop
0	string		8BPS			image/x-photoshop

# <AUTHOR> <EMAIL>
0	string		d8:announce		application/x-bittorrent


# lotus 1-2-3 document
0	belong	0x00001a00	application/x-123
0	belong	0x00000200 	application/x-123

# MS Access database
4	string	Standard\ Jet\ DB	application/msaccess

## magic for XBase files
#0      byte       0x02	
#>8     leshort	  >0
#>>12   leshort    0	application/x-dbf
#
#0      byte       0x03	
#>8     leshort	  >0
#>>12   leshort    0	application/x-dbf
#
#0      byte       0x04	
#>8     leshort	  >0
#>>12   leshort    0	application/x-dbf
#
#0      byte       0x05	
#>8     leshort	  >0
#>>12   leshort    0	application/x-dbf
#
#0      byte       0x30
#>8     leshort	  >0
#>>12   leshort    0	application/x-dbf
#
#0      byte       0x43
#>8     leshort	  >0
#>>12   leshort    0	application/x-dbf
#
#0      byte       0x7b
#>8     leshort	  >0
#>>12   leshort    0	application/x-dbf
#
#0      byte       0x83	
#>8     leshort	  >0
#>>12   leshort    0	application/x-dbf
#
#0      byte       0x8b
#>8     leshort	  >0
#>>12   leshort    0	application/x-dbf
#
#0      byte       0x8e	
#>8     leshort	  >0
#>>12   leshort    0	application/x-dbf
#
#0      byte       0xb3
#>8     leshort	  >0
#>>12   leshort    0	application/x-dbf
#
#0      byte       0xf5
#>8     leshort	  >0
#>>12   leshort    0	application/x-dbf
#
#0	leshort		0x0006		application/x-dbt

# Debian has entries for the old PGP formats:
# pgp:  file(1) magic for Pretty Good Privacy
# see http://lists.gnupg.org/pipermail/gnupg-devel/1999-September/016052.html
#text/PGP key public ring
0	beshort		0x9900			application/pgp
#text/PGP key security ring
0	beshort		0x9501			application/pgp
#text/PGP key security ring
0	beshort		0x9500			application/pgp
#text/PGP encrypted data
0	beshort		0xa600			application/pgp-encrypted
#text/PGP armored data
##public key block
2	string	---BEGIN\ PGP\ PUBLIC\ KEY\ BLOCK-	application/pgp-keys
0	string	-----BEGIN\040PGP\40MESSAGE-			application/pgp
0	string	-----BEGIN\040PGP\40SIGNATURE-			application/pgp-signature
#
# GnuPG Magic:
# 
#
#text/GnuPG key public ring
0	beshort		0x9901			application/pgp
#text/OpenPGP data
0	beshort		0x8501			application/pgp-encrypted	

# flash:        file(1) magic for Macromedia Flash file format
#
# See
#
#       http://www.macromedia.com/software/flash/open/
#
0	string		FWS             
>3	byte		x			application/x-shockwave-flash

# The following paramaters are created for Namazu.
# <http://www.namazu.org/>
#
# 1999/08/13
#0	string		\<!--\ MHonArc		text/html; x-type=mhonarc
0	string		BZh			application/x-bzip2

# 1999/09/09
# VRML (suggested by Masao Takaku)
0	string		#VRML\ V1.0\ ascii	model/vrml
0	string		#VRML\ V2.0\ utf8	model/vrml

#------------------------------------------------------------------------------
# ichitaro456: file(1) magic for Just System Word Processor Ichitaro
#
# Contributor kenzo-:
# Reversed-engineered JS Ichitaro magic numbers
#

0	string		DOC
>43	byte		0x14		application/ichitaro4
>144	string	JDASH		application/ichitaro4

0	string		DOC
>43	byte		0x15		application/ichitaro5

0	string		DOC
>43	byte		0x16		application/ichitaro6

#------------------------------------------------------------------------------
# office97: file(1) magic for MicroSoft Office files
#
# Contributor kenzo-:
# Reversed-engineered MS Office magic numbers
#

#0       string          \320\317\021\340\241\261\032\341
#>48     byte            0x1B            application/excel

2080	string	Microsoft\ Excel\ 5.0\ Worksheet	application/excel
2114	string	Biff5					application/excel

0       string	\224\246\056		application/msword

0	belong	0x31be0000		application/msword

0	string	PO^Q`			application/msword

0	string	\320\317\021\340\241\261\032\341
>546	string	bjbj			application/msword
>546	string	jbjb			application/msword

512	string	R\0o\0o\0t\0\ \0E\0n\0t\0r\0y	application/msword

2080	string	Microsoft\ Word\ 6.0\ Document	application/msword
2080	string	Documento\ Microsoft\ Word\ 6	application/msword
2112	string	MSWordDoc			application/msword

#0	string	\320\317\021\340\241\261\032\341	application/powerpoint
0	string	\320\317\021\340\241\261\032\341	application/msword

0       string  #\ PaCkAgE\ DaTaStReAm  application/x-svr4-package


# WinNT/WinCE PE files (Warner Losh, <EMAIL>)
#
128		string	PE\000\000	application/octet-stream
0		string	PE\000\000	application/octet-stream

# miscellaneous formats
0		string	LZ		application/octet-stream


# .EXE formats (Greg Roelofs, <EMAIL>)
#
0		string	MZ
>24		string	@		application/octet-stream

0		string	MZ
>30		string	Copyright\ 1989-1990\ PKWARE\ Inc.	application/x-zip

0		string	MZ
>30		string	PKLITE\ Copr.	application/x-zip

0		string	MZ
>36		string	LHa's\ SFX	application/x-lha

0		string	MZ		application/octet-stream

# LHA archiver
2		string	-lh
>6		string	-		application/x-lha


# Zoo archiver
20		lelong	0xfdc4a7dc	application/x-zoo

# ARC archiver
0       	lelong&0x8080ffff	0x0000081a	application/x-arc
0		lelong&0x8080ffff	0x0000091a	application/x-arc
0		lelong&0x8080ffff	0x0000021a	application/x-arc
0		lelong&0x8080ffff	0x0000031a	application/x-arc
0		lelong&0x8080ffff	0x0000041a	application/x-arc
0		lelong&0x8080ffff	0x0000061a	application/x-arc

# Microsoft Outlook's Transport Neutral Encapsulation Format (TNEF)
0		lelong	0x223e9f78	application/ms-tnef

# From: stephane.loeuillet@tiscali.f
# http://www.djvuzone.org/
0	string		AT&TFORM	image/x.djvu

# <AUTHOR> <EMAIL>
# this are adrift (adventure game standard) game files, extension .taf
# depending on version magic continues with 0x93453E6139FA (V 4.0)
# 0x9445376139FA (V 3.90)
# 0x9445366139FA (V 3.80)
# this is from source (http://www.adrift.org.uk/) and I have some taf
# files, and checked them.
#0	belong	0x3C423FC9
#>4	belong	0x6A87C2CF	application/x-adrift
#0	string	\000\000\001\000	image/x-ico

# Quark Xpress 3 Files:
# (made the mimetype up) 
0	string	\0\0MMXPR3\0	application/x-quark-xpress-3

# EET archive
# <AUTHOR> <EMAIL>
0	belong	0x1ee7ff00	application/x-eet

# From: Denis Knauf, via gentoo.
0	string	fLaC		audio/x-flac
0	string	CWS		application/x-shockwave-flash

# Gnumeric spreadsheet
# This entry is only semi-helpful, as Gnumeric compresses its files, so
# they will ordinarily reported as "compressed", but at least -z helps
39      string          =<gmr:Workbook           application/x-gnumeric

